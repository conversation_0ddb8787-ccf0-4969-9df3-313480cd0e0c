import 'dart:math';
import 'package:flutter/material.dart';
import 'package:guests/ok_colors.dart';

/// 颜色对比度检查工具
/// 用于验证颜色组合是否符合 WCAG 无障碍标准
class ColorContrastChecker {
  /// WCAG AA 标准：正常文本最小对比度
  static const double wcagAANormal = 4.5;
  
  /// WCAG AA 标准：大文本最小对比度
  static const double wcagAALarge = 3.0;
  
  /// WCAG AAA 标准：正常文本最小对比度
  static const double wcagAAANormal = 7.0;
  
  /// WCAG AAA 标准：大文本最小对比度
  static const double wcagAAALarge = 4.5;

  /// 计算两个颜色之间的对比度
  /// 返回值范围：1.0 (无对比度) 到 21.0 (最大对比度)
  static double calculateContrast(Color color1, Color color2) {
    final luminance1 = _calculateLuminance(color1);
    final luminance2 = _calculateLuminance(color2);
    
    final lighter = max(luminance1, luminance2);
    final darker = min(luminance1, luminance2);
    
    return (lighter + 0.05) / (darker + 0.05);
  }

  /// 计算颜色的相对亮度
  static double _calculateLuminance(Color color) {
    final r = _linearizeColorComponent(color.red / 255.0);
    final g = _linearizeColorComponent(color.green / 255.0);
    final b = _linearizeColorComponent(color.blue / 255.0);
    
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }

  /// 线性化颜色分量
  static double _linearizeColorComponent(double component) {
    if (component <= 0.03928) {
      return component / 12.92;
    } else {
      return pow((component + 0.055) / 1.055, 2.4).toDouble();
    }
  }

  /// 检查对比度是否符合 WCAG AA 标准
  static bool meetsWCAGAA(Color foreground, Color background, {bool isLargeText = false}) {
    final contrast = calculateContrast(foreground, background);
    final threshold = isLargeText ? wcagAALarge : wcagAANormal;
    return contrast >= threshold;
  }

  /// 检查对比度是否符合 WCAG AAA 标准
  static bool meetsWCAGAAA(Color foreground, Color background, {bool isLargeText = false}) {
    final contrast = calculateContrast(foreground, background);
    final threshold = isLargeText ? wcagAAALarge : wcagAAANormal;
    return contrast >= threshold;
  }

  /// 获取对比度等级描述
  static String getContrastRating(Color foreground, Color background, {bool isLargeText = false}) {
    final contrast = calculateContrast(foreground, background);
    
    if (meetsWCAGAAA(foreground, background, isLargeText: isLargeText)) {
      return 'AAA (优秀)';
    } else if (meetsWCAGAA(foreground, background, isLargeText: isLargeText)) {
      return 'AA (良好)';
    } else {
      return '不符合标准';
    }
  }

  /// 验证项目中的关键颜色组合
  static Map<String, Map<String, dynamic>> validateProjectColors() {
    final results = <String, Map<String, dynamic>>{};
    
    // 主要文本颜色组合
    results['主要文本 (onSurface)'] = _analyzeColorPair(
      OkColors.onSurface,
      OkColors.surface,
      '标题文本在白色背景上',
    );
    
    results['次要文本 (onSurfaceVariant)'] = _analyzeColorPair(
      OkColors.onSurfaceVariant,
      OkColors.surface,
      '内容文本在白色背景上',
    );
    
    // 主色调组合
    results['主色调文本'] = _analyzeColorPair(
      Colors.white,
      OkColors.primary,
      '白色文本在主色调背景上',
    );
    
    // 背景颜色组合
    results['背景文本'] = _analyzeColorPair(
      OkColors.onSurface,
      OkColors.background,
      '主要文本在主背景上',
    );
    
    // 边框颜色
    results['边框对比'] = _analyzeColorPair(
      OkColors.outline,
      OkColors.surface,
      '边框在白色背景上',
    );
    
    return results;
  }

  static Map<String, dynamic> _analyzeColorPair(Color foreground, Color background, String description) {
    final contrast = calculateContrast(foreground, background);
    final meetsAA = meetsWCAGAA(foreground, background);
    final meetsAAA = meetsWCAGAAA(foreground, background);
    
    return {
      'description': description,
      'contrast': contrast,
      'meetsAA': meetsAA,
      'meetsAAA': meetsAAA,
      'rating': getContrastRating(foreground, background),
      'foreground': foreground,
      'background': background,
    };
  }

  /// 打印颜色验证报告
  static void printValidationReport() {
    print('\n=== 颜色对比度验证报告 ===\n');
    
    final results = validateProjectColors();
    
    for (final entry in results.entries) {
      final name = entry.key;
      final data = entry.value;
      
      print('$name:');
      print('  描述: ${data['description']}');
      print('  对比度: ${data['contrast'].toStringAsFixed(2)}:1');
      print('  等级: ${data['rating']}');
      print('  WCAG AA: ${data['meetsAA'] ? '✅ 通过' : '❌ 不通过'}');
      print('  WCAG AAA: ${data['meetsAAA'] ? '✅ 通过' : '❌ 不通过'}');
      print('');
    }
    
    print('=== 建议 ===');
    print('• 确保所有文本至少达到 WCAG AA 标准 (4.5:1)');
    print('• 大文本 (18pt+ 或 14pt+ 粗体) 可以使用较低的对比度 (3:1)');
    print('• 非文本元素 (图标、边框) 建议至少 3:1 的对比度');
    print('• 考虑为重要信息提供 AAA 级别的对比度 (7:1)');
  }
}
