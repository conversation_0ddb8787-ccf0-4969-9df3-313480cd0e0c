import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:guests/app/components/blank.dart';
import 'package:guests/app/components/custom_list_tile.dart';
import 'package:guests/app/components/dialog_custom.dart';
import 'package:guests/app/components/future_progress.dart';
import 'package:guests/app/components/invoice_page.dart';
import 'package:guests/app/components/label_value.dart';
import 'package:guests/app/models/order_detail_model.dart';
import 'package:guests/app/modules/order_detail/controllers/order_detail_controller.dart';
import 'package:guests/constants.dart';
import 'package:guests/enums.dart';
import 'package:guests/extension.dart';
import 'package:guests/ok_colors.dart';
import 'package:screenshot/screenshot.dart';

class OrderDetailView extends GetView<OrderDetailController> {
  const OrderDetailView({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('OKpay'),
        // actions: _actions().toList(growable: false),
      ),
      body: ClipRRect(
        borderRadius: kTopRadius,
        child: Stack(
          alignment: Alignment.topCenter,
          children: _children().toList(growable: false),
        ),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    // 發票截圖
    yield Obx(() {
      if (controller.invoiceVisible) {
        final ret = Screenshot(
          controller: controller.screenshotController,
          child: InvoicePage(
            controller.data.toInvoice(),
            storeName: controller.prefProvider.storeName,
            productName: controller.productName,
          ),
        );
        controller.widgetUpdater.complete();
        return ret;
      }
      return const SizedBox.shrink();
    });
    // 國防布
    yield const ColoredBox(
      color: OkColors.background,
      child: SizedBox.expand(),
    );
    // 使用者實際看到的預覽介面
    yield _body();
  }

  Future<void> _showDialog() async {
    controller.draft.orderStatus = controller.data.orderStatus;
    final res = await DialogCustom.showConfirm(
      titleText: '修改訂單狀態',
      content: Obx(() => _radioDialog()),
      icon: DialogIcon.none,
    );
    if (Button.Positive == res && controller.draft.orderStatus.isRefunded) {
      await FutureProgress(
        future: controller.refundOrder(),
      ).dialog();
      await controller.onRefresh();
    }
  }

  Widget _radioDialog() {
    Iterable<Widget> children() sync* {
      yield RadioListTile<OrderStatus>(
        visualDensity: const VisualDensity(
          horizontal: VisualDensity.minimumDensity,
        ),
        onChanged: (value) {
          controller.draft.orderStatus = value!;
          controller.refreshDraft();
        },
        activeColor: OkColors.primary,
        value: OrderStatus.Completed,
        groupValue: controller.draft.orderStatus,
        title: Text(
          '訂單完成',
          style: TextStyle(
            fontSize: 20,
            color: controller.draft.orderStatus.isCompleted
                ? OkColors.primary
                : Colors.black,
          ),
          textAlign: TextAlign.left,
        ),
      );
      yield RadioListTile<OrderStatus>(
        visualDensity: const VisualDensity(
          horizontal: VisualDensity.minimumDensity,
        ),
        onChanged: (value) {
          controller.draft.orderStatus = value!;
          controller.refreshDraft();
        },
        activeColor: OkColors.primary,
        value: OrderStatus.Refunded,
        groupValue: controller.draft.orderStatus,
        title: Text(
          // '退款並作廢發票',
          controller.data.displayRefound,
          style: TextStyle(
            fontSize: 20,
            color: controller.draft.orderStatus.isRefunded
                ? OkColors.primary
                : Colors.black,
          ),
          textAlign: TextAlign.left,
        ),
      );
    }

    return IntrinsicWidth(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: children().toList(growable: false),
      ),
    );
  }

  Widget _body() {
    Iterable<Widget> children() sync* {
      yield _Header(
        data: controller.data,
        onPressed: _showDialog,
      );
      yield Expanded(child: _page());
    }

    return controller.obx(
      (state) {
        return Column(
          children: children().toList(growable: false),
        );
      },
      onEmpty: const Blank(),
      onError: (error) => const Blank(),
    );
  }

  Widget _page() {
    Iterable<Widget> children() sync* {
      yield ColoredBox(
        color: Colors.white,
        child: _Page1(
          data: controller.data,
          onPressed: controller.print,
        ),
      );
      yield ColoredBox(
        color: const Color(0xffe2e2e2),
        child: _Page2(
          data: controller.data,
        ),
      );
    }

    return ListView(
      children: children().toList(growable: false),
    );
  }

  Iterable<Widget> _actions() sync* {
    yield TextButton(
      onPressed: _onInvoicePressed,
      child: Text("補上傳發票"),
    );
    yield TextButton(
      onPressed: _onCancelPressed,
      child: Text("補作廢發票"),
    );
  }

  Future<void> _onCancelPressed() async {
    final invoiceNumber = await FutureProgress(
      future: controller.uploadCancel(),
    ).dialog();
    kLogger.d('[OrderDetailView] Cancel invoiceNumber: ' + invoiceNumber);
  }

  Future<void> _onInvoicePressed() async {
    final invoiceNumber = await FutureProgress(
      future: controller.uploadInvoice(),
    ).dialog();
    kLogger.d('[OrderDetailView] Upload invoiceNumber: ' + invoiceNumber);
  }
}

class _Header extends StatelessWidget {
  final OrderDetail data;
  final VoidCallback? onPressed;

  const _Header({
    Key? key,
    required this.data,
    this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: OkColors.surfaceVariant,
      ),
      height: Constants.buttonHeight,
      padding: const EdgeInsets.symmetric(
        horizontal: Constants.paddingHorizontal,
      ),
      alignment: Alignment.centerRight,
      child: Visibility(
        visible: data.orderStatus.isCompleted,
        child: _completedWidget(),
        replacement: _refundWidget(),
      ),
    );
  }

  Widget _completedWidget() {
    return ElevatedButton.icon(
      iconAlignment: IconAlignment.end,
      style: ElevatedButton.styleFrom(
        backgroundColor: OkColors.surfaceContainer,
        padding: kChipPadding,
        side: const BorderSide(
          width: 1.0,
          color: Colors.white,
        ),
        shape: const StadiumBorder(),
        minimumSize: Size.zero,
      ),
      onPressed: onPressed,
      label: Text(
        '訂單完成',
        style: TextStyle(
          fontSize: 16,
          color: OkColors.onSurface,
        ),
        textAlign: TextAlign.center,
      ),
      icon: Icon(
        Icons.expand_more,
        color: OkColors.onSurface,
      ),
    );
  }

  Widget _refundWidget() {
    return Text(
      data.displayStatus,
      style: TextStyle(
        fontSize: 16,
        color: data.displayStatusColor,
      ),
      textAlign: TextAlign.left,
    );
  }
}

class _Page1 extends StatelessWidget {
  final OrderDetail data;
  final VoidCallback? onPressed;

  const _Page1({
    Key? key,
    required this.data,
    this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: _children().toList(growable: false),
    );
  }

  Widget _invoice() {
    Iterable<Widget> children() sync* {
      yield LabelValue(
        labelText: '發票號碼：',
        valueText: data.invoice?.number ?? '',
      );
      if (data.invoice != null) {
        yield const SizedBox(width: 4.0);
        if (data.orderStatus.isCompleted) {
          yield ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xff3e4b5a),
              padding: kChipPadding,
              shape: const StadiumBorder(),
              side: const BorderSide(
                width: 1.0,
                color: Colors.white,
              ),
              minimumSize: Size.zero,
            ),
            onPressed: onPressed,
            child: const Text(
              '補印發票',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          );
        } else {
          yield Text(
            '作廢',
            style: const TextStyle(
              fontSize: 16.0,
              color: Colors.black,
            ),
          );
        }
      }
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    yield const SizedBox(height: kDefaultPadding);
    yield CustomListTile(
      padding: Constants.paddingContent,
      leadingText: '訂單編號：',
      titleText: data.orderNumber ?? '',
      trailText: data.displayDateTimeMdHm,
    );
    yield _invoice().paddingSymmetric(horizontal: Constants.paddingHorizontal);
    yield CustomListTile(
      padding: Constants.paddingContent,
      leadingText: '統一編號：',
      titleText: data.invoice?.vatNumber,
    );
    yield const SizedBox(height: kDefaultPadding);
  }
}

class _Page2 extends StatelessWidget {
  final OrderDetail data;

  const _Page2({
    Key? key,
    required this.data,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: _children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    yield const SizedBox(height: 16.0);
    yield Padding(
      padding: const EdgeInsets.symmetric(horizontal: kPadding),
      child: _info(),
    );
    yield const SizedBox(height: 16.0);
    yield SizedBox(
      width: double.infinity,
      child: SvgPicture.asset(
        'assets/images/dot_line_02.svg',
        fit: BoxFit.fitWidth,
      ),
    );
    yield const SizedBox(height: 22.0);
    yield Row(
      children: [
        Expanded(
          child: Builder(
            builder: (context) {
              final ls = data.orderItems;
              final v =
                  ls?.isEmpty == true ? '0' : ls!.first.finalPrice?.currency;
              return LabelValue(
                labelText: '商品小計: ',
                // valueText: '0',
                valueText: v ?? '',
              );
            },
          ),
        ),
        Expanded(
          child: LabelValue(
            labelText: '服務費(0%): ',
            valueText: '0',
          ),
        ),
      ],
    ).paddingSymmetric(
      horizontal: kPadding,
    );
    yield const SizedBox(height: 18.0);
    yield Row(
      children: [
        Expanded(
          child: Builder(
            builder: (context) {
              final ls = data.orderDiscount;
              final v =
                  ls?.isEmpty == true ? '0' : ls!.first.discountPrice?.currency;
              return LabelValue(
                labelText: '現場折價: ',
                // valueText: '0',
                valueText: v ?? '',
              );
            },
          ),
        ),
        Expanded(
          child: LabelValue(
            labelText: '額外費用: ',
            // valueText: '0',
            valueText: data.additionalCharges.currency,
          ),
        ),
      ],
    ).paddingSymmetric(
      horizontal: kPadding,
    );
    yield const SizedBox(height: 22.0);
    yield SizedBox(
      width: double.infinity,
      child: SvgPicture.asset(
        'assets/images/dot_line_02.svg',
        fit: BoxFit.fitWidth,
      ),
    );
    yield const SizedBox(height: 18.0);
    yield LabelValue(
      labelText: '商品總價: ',
      // valueText: '\$${(data.total ?? 0).currency}',
      valueText: '\$${(data.orderPayment?.total ?? 0).currency}',
      labelStyle: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.w700,
        color: Colors.black,
      ),
      valueStyle: const TextStyle(
        fontSize: 20,
        color: OkColors.primary,
        fontWeight: FontWeight.w700,
      ),
    ).paddingSymmetric(
      horizontal: kPadding,
    );
    yield const SizedBox(height: kDefaultPadding);
    yield Row(
      children: [
        Expanded(
          child: LabelValue(
            labelText: '實收: ',
            // valueText: '0',
            valueText: data.orderPayment?.paid.currency ?? '',
          ),
        ),
        Expanded(
          child: LabelValue(
            labelText: '找零: ',
            // valueText: '0',
            valueText: data.orderPayment?.change.currency ?? '',
          ),
        ),
      ],
    ).paddingSymmetric(
      horizontal: kPadding,
    );
    yield const SizedBox(height: 18.0);
    yield SizedBox(
      width: double.infinity,
      child: SvgPicture.asset(
        'assets/images/dot_line_02.svg',
        fit: BoxFit.fitWidth,
      ),
    );
    yield const SizedBox(height: kDefaultPadding);
  }

  Widget _info() {
    Iterable<InlineSpan> children() sync* {
      yield TextSpan(
        text: '1 ',
        style: const TextStyle(
          fontWeight: FontWeight.w500,
        ),
      );
      yield TextSpan(
        text: '項，',
        style: const TextStyle(
          color: Colors.black,
          fontWeight: FontWeight.w500,
        ),
      );
      yield TextSpan(
        text: '\$${(data.total ?? 0).currency}',
        style: const TextStyle(
          fontWeight: FontWeight.w500,
        ),
      );
      yield TextSpan(
        text: ' 元',
        style: const TextStyle(
          color: const Color(0xff222222),
          fontWeight: FontWeight.w500,
        ),
      );
    }

    return Text.rich(
      TextSpan(
        style: TextStyle(
          fontSize: 16,
          color: OkColors.primary,
        ),
        children: children().toList(growable: false),
      ),
      textHeightBehavior: TextHeightBehavior(applyHeightToFirstAscent: false),
      textAlign: TextAlign.left,
    );
  }
}
