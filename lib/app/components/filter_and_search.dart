import 'package:flutter/material.dart';
import 'package:guests/constants.dart';

class FilterAndSearch extends StatelessWidget {
  final Iterable<String> tabs;
  final ValueChanged<int>? onPressed;
  final ValueChanged<String>? onChanged;
  final String? hintText;

  const FilterAndSearch({
    Key? key,
    this.tabs = const ['今日', '全部'],
    this.onPressed,
    this.onChanged,
    this.hintText,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        children: _children(context).toList(growable: false),
      ),
    );
  }

  Widget _filter() {
    return ClipRRect(
      borderRadius: kBorderRadius,
      child: Container(
        // decoration: BoxDecoration(
        //   borderRadius: BorderRadius.circular(kRadius),
        //   color: Colors.white,
        // ),
        color: Colors.white,
        width: 140.0,
        height: kButtonHeight,
        child: DefaultTabController(
          length: tabs.length,
          child: TabBar(
            onTap: onPressed,
            tabs: List.generate(
              tabs.length,
              (index) {
                return Tab(
                  text: tabs.elementAt(index),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _search(BuildContext context) {
    return TextFormField(
      onChanged: this.onChanged,
      decoration: InputDecoration(
        contentPadding: EdgeInsets.zero,
        filled: true,
        fillColor: Theme.of(context).colorScheme.surface,
        enabledBorder: const OutlineInputBorder(
          borderSide: BorderSide.none,
          borderRadius: kBorderRadius,
        ),
        focusedBorder: const OutlineInputBorder(
          borderSide: BorderSide.none,
          borderRadius: kBorderRadius,
        ),
        prefixIcon: Icon(
          Icons.search,
          color: Theme.of(context).colorScheme.outline,
        ),
        hintText: this.hintText ?? '請輸入關鍵字',
        hintStyle: TextStyle(
          fontSize: 16,
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
      ),
    );
  }

  Iterable<Widget> _children(BuildContext context) sync* {
    yield _filter();
    yield const SizedBox(width: 8.0);
    yield Expanded(child: _search(context));
  }
}
