import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:guests/color_contrast_checker.dart';
import 'package:guests/ok_colors.dart';

void main() {
  group('颜色对比度测试', () {
    test('验证项目颜色对比度', () {
      print('\n=== Flutter 项目颜色对比度验证报告 ===\n');
      
      final results = ColorContrastChecker.validateProjectColors();
      
      for (final entry in results.entries) {
        final name = entry.key;
        final data = entry.value;
        
        print('$name:');
        print('  描述: ${data['description']}');
        print('  对比度: ${data['contrast'].toStringAsFixed(2)}:1');
        print('  等级: ${data['rating']}');
        print('  WCAG AA: ${data['meetsAA'] ? '✅ 通过' : '❌ 不通过'}');
        print('  WCAG AAA: ${data['meetsAAA'] ? '✅ 通过' : '❌ 不通过'}');
        print('');
      }
      
      print('=== 建议 ===');
      print('• 确保所有文本至少达到 WCAG AA 标准 (4.5:1)');
      print('• 大文本 (18pt+ 或 14pt+ 粗体) 可以使用较低的对比度 (3:1)');
      print('• 非文本元素 (图标、边框) 建议至少 3:1 的对比度');
      print('• 考虑为重要信息提供 AAA 级别的对比度 (7:1)');
      
      // 验证关键颜色组合符合标准
      expect(
        ColorContrastChecker.meetsWCAGAA(OkColors.onSurface, OkColors.surface),
        true,
        reason: '主要文本颜色必须符合 WCAG AA 标准',
      );
      
      expect(
        ColorContrastChecker.meetsWCAGAA(OkColors.onSurfaceVariant, OkColors.surface),
        true,
        reason: '次要文本颜色必须符合 WCAG AA 标准',
      );
    });

    test('验证具体颜色对比度计算', () {
      // 测试黑白对比度 (应该是 21:1)
      final blackWhiteContrast = ColorContrastChecker.calculateContrast(
        Colors.black,
        Colors.white,
      );
      expect(blackWhiteContrast, closeTo(21.0, 0.1));

      // 测试相同颜色对比度 (应该是 1:1)
      final sameColorContrast = ColorContrastChecker.calculateContrast(
        Colors.red,
        Colors.red,
      );
      expect(sameColorContrast, closeTo(1.0, 0.1));

      // 测试项目主色调对比度
      final primaryContrast = ColorContrastChecker.calculateContrast(
        Colors.white,
        OkColors.primary,
      );
      print('主色调 vs 白色对比度: ${primaryContrast.toStringAsFixed(2)}:1');
    });

    test('验证颜色等级评定', () {
      // 测试高对比度组合
      final highContrastRating = ColorContrastChecker.getContrastRating(
        Colors.black,
        Colors.white,
      );
      expect(highContrastRating, contains('AAA'));

      // 测试项目颜色组合
      final projectRating = ColorContrastChecker.getContrastRating(
        OkColors.onSurface,
        OkColors.surface,
      );
      print('项目主要文本颜色等级: $projectRating');
    });
  });
}
